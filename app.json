{"expo": {"name": "Nutri AI", "slug": "nutri-ai-mobile", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": false, "runtimeVersion": "1.0.0", "platforms": ["ios", "android"], "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#FFFFFF"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.saim7777.nutriai", "buildNumber": "1", "icon": "./assets/icon.png", "requireFullScreen": false, "userInterfaceStyle": "light", "deploymentTarget": "15.1", "infoPlist": {"NSMotionUsageDescription": "This app uses motion sensors to track your steps and activity for health monitoring.", "NSHealthShareUsageDescription": "This app needs access to health data to provide personalized nutrition recommendations.", "NSHealthUpdateUsageDescription": "This app needs to update health data to track your nutrition progress.", "NSHealthClinicalHealthRecordsShareUsageDescription": "This app needs access to health records to provide comprehensive nutrition analysis.", "NSMicrophoneUsageDescription": "This app needs access to the microphone for voice-to-text input in Ask AI and Recipe search features.", "NSSpeechRecognitionUsageDescription": "This app uses speech recognition to convert your voice to text for easier input.", "NSCameraUsageDescription": "This app needs access to the camera to scan food items and analyze meals for nutrition tracking.", "NSPhotoLibraryUsageDescription": "This app needs access to your photo library to select images for meal analysis.", "NSPhotoLibraryAddUsageDescription": "This app needs access to save meal photos to your photo library.", "NSLocationWhenInUseUsageDescription": "This app needs location access to find nearby restaurants and provide location-based nutrition recommendations.", "NSUserNotificationsUsageDescription": "This app sends meal reminders and achievement notifications to help you stay on track with your nutrition goals.", "NSFaceIDUsageDescription": "This app uses Face ID for secure access to your nutrition data.", "UIBackgroundModes": ["background-processing", "background-fetch"], "UIRequiredDeviceCapabilities": ["arm64"], "UIStatusBarStyle": "UIStatusBarStyleDefault", "UIViewControllerBasedStatusBarAppearance": false, "ITSAppUsesNonExemptEncryption": false}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "icon": "./assets/icon.png", "versionCode": 1, "compileSdkVersion": 34, "targetSdkVersion": 34, "permissions": ["android.permission.ACTIVITY_RECOGNITION", "android.permission.BODY_SENSORS", "android.permission.POST_NOTIFICATIONS", "android.permission.RECORD_AUDIO", "android.permission.MODIFY_AUDIO_SETTINGS", "android.permission.INTERNET", "android.permission.CAMERA", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_COARSE_LOCATION", "android.permission.RECEIVE_BOOT_COMPLETED", "android.permission.WAKE_LOCK", "android.permission.VIBRATE", "android.permission.FOREGROUND_SERVICE", "android.permission.SCHEDULE_EXACT_ALARM", "android.permission.USE_EXACT_ALARM"], "package": "com.saim7777.nutriai"}, "updates": {"enabled": false, "checkAutomatically": "ON_LOAD", "fallbackToCacheTimeout": 0}, "plugins": ["expo-av", "expo-location", "expo-image-picker", "expo-camera", ["expo-notifications", {"icon": "./assets/adaptive-icon.png", "color": "#ffffff", "sounds": [], "ios": {"allowAlert": true, "allowBadge": true, "allowSound": true, "allowAnnouncements": true}, "android": {"allowAlert": true, "allowBadge": true, "allowSound": true}}], ["expo-build-properties", {"android": {"compileSdkVersion": 34, "targetSdkVersion": 34, "buildToolsVersion": "34.0.0"}, "ios": {"deploymentTarget": "15.1"}}]], "assetBundlePatterns": ["**/*"], "extra": {"eas": {"projectId": "1912bc1a-2843-47ba-b38b-0a4ba8f5c6d7"}}, "owner": "saim7777", "scheme": "<PERSON><PERSON><PERSON>"}}