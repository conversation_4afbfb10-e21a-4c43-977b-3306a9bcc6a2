const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Add support for additional file extensions
config.resolver.assetExts.push('lottie');

// Ensure proper transformer configuration
config.transformer.babelTransformerPath = require.resolve('metro-react-native-babel-transformer');

// Add Node.js module resolution for markdown-it dependencies
config.resolver.alias = {
  ...config.resolver.alias,
  punycode: require.resolve('punycode'),
};

// Configure resolver to handle Node.js modules
config.resolver.platforms = ['ios', 'android', 'native', 'web'];

module.exports = config;
