import React from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Platform,
  Text,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
  runOnJS,
  withSequence,
  withDelay,
  Easing,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';

const { width: screenWidth } = Dimensions.get('window');

interface TabBarProps {
  state: any;
  descriptors: any;
  navigation: any;
}

interface TabConfig {
  name: string;
  icon: keyof typeof Ionicons.glyphMap;
  iconFocused: keyof typeof Ionicons.glyphMap;
  label: string;
  color: string;
  isSpecial?: boolean;
}

const tabConfigs: Record<string, TabConfig> = {
  Home: {
    name: 'Home',
    icon: 'home-outline',
    iconFocused: 'home',
    label: 'Home',
    color: '#6B7C5A',
  },
  Recipes: {
    name: 'Recipes',
    icon: 'restaurant-outline',
    iconFocused: 'restaurant',
    label: 'Recipes',
    color: '#6B7C5A',
  },
  Scanner: {
    name: 'Scanner',
    icon: 'scan-outline',
    iconFocused: 'scan',
    label: 'Scan',
    color: '#6B7C5A',
    isSpecial: true,
  },
  Plan: {
    name: 'Plan',
    icon: 'calendar-outline',
    iconFocused: 'calendar',
    label: 'Plan',
    color: '#6B7C5A',
  },
  Profile: {
    name: 'Profile',
    icon: 'person-outline',
    iconFocused: 'person',
    label: 'Profile',
    color: '#6B7C5A',
  },
};

// Premium Tab Item Component
const PremiumTabItem: React.FC<{
  config: TabConfig;
  isActive: boolean;
  onPress: () => void;
  index: number;
}> = ({ config, isActive, onPress, index }) => {
  const scale = useSharedValue(1);
  const translateY = useSharedValue(0);
  const glowOpacity = useSharedValue(0);
  const labelOpacity = useSharedValue(isActive ? 1 : 0);
  const iconScale = useSharedValue(1);

  React.useEffect(() => {
    // Scale animation
    scale.value = withSpring(isActive ? 1.1 : 1, {
      damping: 15,
      stiffness: 300,
    });

    // Vertical movement for active state
    translateY.value = withSpring(isActive ? -4 : 0, {
      damping: 20,
      stiffness: 200,
    });

    // Glow effect for active state
    glowOpacity.value = withTiming(isActive ? 1 : 0, {
      duration: 300,
      easing: Easing.out(Easing.quad),
    });

    // Label fade animation
    labelOpacity.value = withTiming(isActive ? 1 : 0, {
      duration: 200,
    });

    // Icon pulse for active state
    if (isActive) {
      iconScale.value = withSequence(
        withTiming(1.2, { duration: 150 }),
        withTiming(1, { duration: 150 })
      );
    }
  }, [isActive]);

  const handlePress = () => {
    // Haptic feedback
    runOnJS(() => {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    })();

    // Press animation
    scale.value = withSequence(
      withTiming(0.95, { duration: 100 }),
      withSpring(isActive ? 1.1 : 1, { damping: 15, stiffness: 300 })
    );

    onPress();
  };

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: scale.value },
      { translateY: translateY.value },
    ],
  }));

  const glowStyle = useAnimatedStyle(() => ({
    opacity: glowOpacity.value,
  }));

  const labelStyle = useAnimatedStyle(() => ({
    opacity: labelOpacity.value,
  }));

  const iconAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: iconScale.value }],
  }));

  if (config.isSpecial) {
    // Special floating scanner button
    return (
      <TouchableOpacity
        style={styles.specialTabContainer}
        onPress={handlePress}
        activeOpacity={0.8}
      >
        <Animated.View style={[styles.specialTab, animatedStyle]}>
          <LinearGradient
            colors={['#6B7C5A', '#8B9A7A']}
            style={styles.specialTabGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <Animated.View style={iconAnimatedStyle}>
              <Ionicons
                name={isActive ? config.iconFocused : config.icon}
                size={28}
                color="white"
              />
            </Animated.View>
          </LinearGradient>

          {/* Glow effect */}
          <Animated.View style={[styles.specialTabGlow, glowStyle]} />
        </Animated.View>
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity
      style={styles.tabItemContainer}
      onPress={handlePress}
      activeOpacity={1}
    >
      <Animated.View style={[styles.tabItem, animatedStyle]}>
        {/* Background glow for active state */}
        <Animated.View style={[styles.tabItemGlow, glowStyle]} />

        {/* Icon */}
        <Animated.View style={iconAnimatedStyle}>
          <Ionicons
            name={isActive ? config.iconFocused : config.icon}
            size={24}
            color={isActive ? config.color : '#9CA3AF'}
          />
        </Animated.View>

        {/* Label */}
        <Animated.Text style={[styles.tabLabel, labelStyle, { color: config.color }]}>
          {config.label}
        </Animated.Text>
      </Animated.View>
    </TouchableOpacity>
  );
};

const AnimatedTabBar: React.FC<TabBarProps> = ({ state, descriptors, navigation }) => {
  const insets = useSafeAreaInsets();
  const tabBarOpacity = useSharedValue(1);
  const tabBarScale = useSharedValue(1);

  React.useEffect(() => {
    // Entrance animation
    tabBarOpacity.value = withDelay(100, withTiming(1, { duration: 500 }));
    tabBarScale.value = withDelay(100, withSpring(1, { damping: 20, stiffness: 200 }));
  }, []);

  const handleTabPress = (routeName: string, index: number) => {
    const event = navigation.emit({
      type: 'tabPress',
      target: state.routes[index].key,
      canPreventDefault: true,
    });

    if (!event.defaultPrevented) {
      navigation.navigate(routeName);
    }
  };

  const tabBarAnimatedStyle = useAnimatedStyle(() => ({
    opacity: tabBarOpacity.value,
    transform: [{ scale: tabBarScale.value }],
  }));

  return (
    <View style={[styles.container, { paddingBottom: insets.bottom }]}>
      <Animated.View style={[styles.floatingTabBar, tabBarAnimatedStyle]}>
        {/* Background with blur effect */}
        <BlurView intensity={20} style={styles.blurBackground}>
          <LinearGradient
            colors={['rgba(255, 255, 255, 0.95)', 'rgba(255, 255, 255, 0.85)']}
            style={styles.gradientBackground}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          />
        </BlurView>

        {/* Tab Items */}
        <View style={styles.tabContainer}>
          {state.routes.map((route: any, index: number) => {
            const config = tabConfigs[route.name];
            const isActive = index === state.index;

            if (!config) return null;

            return (
              <PremiumTabItem
                key={route.key}
                config={config}
                isActive={isActive}
                onPress={() => handleTabPress(route.name, index)}
                index={index}
              />
            );
          })}
        </View>

        {/* Premium border accent */}
        <View style={styles.borderAccent} />
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'transparent',
    paddingHorizontal: 20,
    paddingBottom: Platform.OS === 'ios' ? 10 : 20,
  },
  floatingTabBar: {
    backgroundColor: 'transparent',
    borderRadius: 32,
    overflow: 'hidden',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.15,
        shadowRadius: 20,
      },
      android: {
        elevation: 12,
      },
    }),
  },
  blurBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: 32,
  },
  gradientBackground: {
    flex: 1,
    borderRadius: 32,
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.1)',
  },
  tabContainer: {
    flexDirection: 'row',
    paddingVertical: 12,
    paddingHorizontal: 8,
    alignItems: 'center',
    justifyContent: 'space-around',
  },
  borderAccent: {
    position: 'absolute',
    top: 0,
    left: '20%',
    right: '20%',
    height: 2,
    backgroundColor: '#6B7C5A',
    borderRadius: 1,
    opacity: 0.3,
  },

  // Regular Tab Item Styles
  tabItemContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabItem: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    minHeight: 56,
    position: 'relative',
  },
  tabItemGlow: {
    position: 'absolute',
    top: -4,
    left: -4,
    right: -4,
    bottom: -4,
    backgroundColor: 'rgba(107, 124, 90, 0.15)',
    borderRadius: 24,
  },
  tabLabel: {
    fontSize: 11,
    fontWeight: '600',
    marginTop: 4,
    letterSpacing: 0.3,
  },

  // Special Scanner Tab Styles
  specialTabContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 8,
  },
  specialTab: {
    width: 64,
    height: 64,
    borderRadius: 32,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  specialTabGradient: {
    width: 64,
    height: 64,
    borderRadius: 32,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 3,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  specialTabGlow: {
    position: 'absolute',
    top: -8,
    left: -8,
    right: -8,
    bottom: -8,
    backgroundColor: 'rgba(107, 124, 90, 0.2)',
    borderRadius: 40,
  },
});

export default AnimatedTabBar;
