import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Platform,
  Alert,
  StatusBar,
  KeyboardAvoidingView,
  Dimensions,
  Image,
  Share,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import * as Clipboard from 'expo-clipboard';
import Markdown from 'react-native-markdown-display';
import Animated, {
  FadeInUp,
  FadeInDown,
  SlideInRight,
  SlideInUp,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
  withSequence,
  withDelay,
  withRepeat,
  Easing,
} from 'react-native-reanimated';

// Import services
import ApiService from '../services/ApiService';
import VoiceService from '../services/VoiceService';

// Import theme
import { Colors, FontSizes, Spacing, BorderRadius, FontWeights, Shadows } from '../constants/Colors';
import { Typography } from '../constants/Typography';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface ChatMessage {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
}

interface QuickQuestionType {
  id: string;
  text: string;
  icon: string;
}

const quickQuestions: QuickQuestionType[] = [
  { id: '1', text: 'What are the best sources of protein?', icon: 'fitness' },
  { id: '2', text: 'How much water should I drink daily?', icon: 'water' },
  { id: '3', text: 'What foods boost metabolism?', icon: 'flash' },
  { id: '4', text: 'Best foods for weight loss?', icon: 'trending-down' },
  { id: '5', text: 'Healthy meal prep ideas?', icon: 'restaurant' },
  { id: '6', text: 'Benefits of intermittent fasting?', icon: 'time' },
];

const AskScreenModern: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputText, setInputText] = useState('');
  const [loading, setLoading] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [voiceText, setVoiceText] = useState('');
  const [typingText, setTypingText] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [displayText, setDisplayText] = useState('');

  const scrollViewRef = useRef<ScrollView>(null);
  const textInputRef = useRef<TextInput>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Animation values
  const scrollY = useSharedValue(0);
  const headerOpacity = useSharedValue(1);

  useEffect(() => {
    // Initialize voice service
    VoiceService.initialize();

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  // Modern smooth typing animation
  useEffect(() => {
    if (isTyping && typingText) {
      setDisplayText('');
      let currentIndex = 0;

      // Instant response - no typing animation delays
      const baseSpeed = 1; // Minimal delay for smooth rendering
      const variableSpeed = 0; // No speed variation
      const pauseChance = 0; // No pauses
      const pauseDuration = 0; // No pause duration

      const typeNextCharacter = () => {
        if (currentIndex < typingText.length) {
          // Add natural variation to typing speed
          const currentChar = typingText[currentIndex];
          let delay = baseSpeed + Math.random() * variableSpeed;

          // No pauses for instant response
          if (['.', '!', '?', ',', ';', ':'].includes(currentChar)) {
            delay += 0;
          }

          // Occasional thinking pauses
          if (Math.random() < pauseChance) {
            delay += pauseDuration;
          }

          // Smooth character reveal
          setDisplayText(typingText.substring(0, currentIndex + 1));
          currentIndex++;

          // Subtle haptic feedback for key characters
          if (['.', '!', '?'].includes(currentChar)) {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          }

          // Schedule next character
          intervalRef.current = setTimeout(typeNextCharacter, delay);
        } else {
          // Typing complete - finalize message
          setTimeout(() => {
            const aiMessage: ChatMessage = {
              id: Date.now().toString(),
              text: typingText,
              isUser: false,
              timestamp: new Date(),
            };
            setMessages(prev => [...prev, aiMessage]);
            setIsTyping(false);
            setTypingText('');
            setDisplayText('');

            // Smooth scroll to bottom
            setTimeout(() => {
              scrollViewRef.current?.scrollToEnd({ animated: true });
            }, 50);
          }, 200); // Much shorter pause before finalizing
        }
      };

      // Start typing with minimal delay
      intervalRef.current = setTimeout(typeNextCharacter, 100);

      return () => {
        if (intervalRef.current) {
          clearTimeout(intervalRef.current);
          intervalRef.current = null;
        }
      };
    }
  }, [isTyping, typingText]);

  const sendMessage = async (text: string) => {
    if (!text.trim()) return;

    // Clear input immediately
    setInputText('');

    // Add user message
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      text: text.trim(),
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setLoading(true);

    // Scroll to show user message
    setTimeout(() => {
      scrollViewRef.current?.scrollToEnd({ animated: true });
    }, 100);

    try {
      console.log('🤖 Sending question to API:', text);
      const responseText = await ApiService.askNutritionQuestion(text);
      console.log('✅ Received response:', responseText.substring(0, 100) + '...');

      setLoading(false);

      // Start typing animation
      setTypingText(responseText);
      setIsTyping(true);
    } catch (error) {
      console.error('❌ Error asking question:', error);
      setLoading(false);

      // Fallback response with typing animation
      const fallbackText = "I'm here to help with your nutrition questions! While I'm currently having trouble connecting to my knowledge base, I'd recommend consulting with a registered dietitian for personalized advice. In the meantime, focus on eating a balanced diet with plenty of fruits, vegetables, lean proteins, and whole grains.";

      setTypingText(fallbackText);
      setIsTyping(true);
    }
  };

  const handleQuickQuestion = (question: string) => {
    setInputText(question);
    sendMessage(question);
  };

  const startVoiceRecognition = async () => {
    try {
      console.log('🎤 Starting voice recognition...');
      const started = await VoiceService.startListening({
        onStart: () => {
          console.log('🎤 Voice recognition started');
          setIsListening(true);
          setVoiceText('');
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        },
        onResult: (result) => {
          console.log('🎤 Voice result:', result);
          if (result.isFinal && result.text.trim()) {
            setVoiceText(result.text);
            setInputText(result.text);
            setIsListening(false);
            Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          }
        },
        onPartialResult: (text) => {
          console.log('🎤 Partial result:', text);
          setVoiceText(text);
        },
        onEnd: () => {
          console.log('🎤 Voice recognition ended');
          setIsListening(false);
        },
        onError: (error) => {
          console.error('🎤 Voice recognition error:', error);
          setIsListening(false);
          setVoiceText('');
          Alert.alert('Voice Recognition Error', error);
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
        }
      });

      if (!started) {
        Alert.alert('Voice Recognition', 'Voice recognition is not available on this device.');
      }
    } catch (error) {
      console.error('Error starting voice recognition:', error);
      Alert.alert('Error', 'Failed to start voice recognition');
    }
  };

  const stopVoiceRecognition = async () => {
    try {
      await VoiceService.stopListening();
      setIsListening(false);
    } catch (error) {
      console.error('Error stopping voice recognition:', error);
    }
  };

  const handleSendPress = () => {
    if (inputText.trim()) {
      sendMessage(inputText);
    }
  };

  const handleInputChange = useCallback((text: string) => {
    setInputText(text);
  }, []);

  const headerStyle = useAnimatedStyle(() => ({
    opacity: headerOpacity.value,
    transform: [{ translateY: interpolate(scrollY.value, [0, 100], [0, -10], 'clamp') }],
  }));

  return (
    <View style={styles.premiumContainer}>
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />

      {/* Premium Header */}
      <Animated.View style={[styles.premiumHeader, headerStyle]} entering={FadeInDown.duration(600)}>
        <SafeAreaView edges={['top']}>
          <View style={styles.premiumHeaderContent}>
            <View style={styles.premiumHeaderTitleSection}>
              <Text style={styles.premiumHeaderTitle}>Ask AI</Text>
              <Text style={styles.premiumHeaderSubtitle}>Nutrition Assistant 🍃</Text>
            </View>
            <TouchableOpacity
              style={styles.premiumHeaderMenuButton}
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                Alert.alert(
                  'Menu Options',
                  'Choose an action',
                  [
                    { text: 'Clear Chat', onPress: () => setMessages([]) },
                    { text: 'Settings', onPress: () => Alert.alert('Settings', 'Coming soon!') },
                    { text: 'Cancel', style: 'cancel' },
                  ]
                );
              }}
            >
              <Ionicons name="ellipsis-horizontal" size={24} color={Colors.brand} />
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      </Animated.View>

      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        {/* Premium Messages Container */}
        <ScrollView
          ref={scrollViewRef}
          style={{ flex: 1 }}
          contentContainerStyle={{ flexGrow: 1 }}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
          keyboardDismissMode="none"
        >
          {messages.length === 0 ? (
            // Welcome Screen with Quick Questions
            <Animated.View entering={FadeInUp.delay(300).duration(800)} style={styles.welcomeSection}>
              <View style={styles.welcomeCard}>
                <View style={styles.welcomeIcon}>
                  <Image
                    source={require('../../assets/image final.png')}
                    style={styles.welcomeIconImage}
                    resizeMode="contain"
                  />
                </View>
                <Text style={styles.welcomeTitle}>Welcome to AI Nutritionist!</Text>
                <Text style={styles.welcomeDescription}>
                  Ask me anything about nutrition, diet, and healthy eating. I'm here to help you make informed decisions about your health.
                </Text>
              </View>

              <View style={styles.quickQuestionsSection}>
                <Text style={styles.quickQuestionsTitle}>Popular Questions</Text>
                <View style={styles.quickQuestionsGrid}>
                  {quickQuestions.map((question, index) => (
                    <QuickQuestion
                      key={question.id}
                      question={question}
                      onPress={() => handleQuickQuestion(question.text)}
                      index={index}
                    />
                  ))}
                </View>
              </View>
            </Animated.View>
          ) : (
            // Chat Messages
            <View style={styles.chatContainer}>
              {messages.map((message) => (
                <MessageBubble key={message.id} message={message} />
              ))}

              {/* Typing Indicator */}
              {isTyping && displayText && (
                <TypingIndicator text={displayText} />
              )}

              {/* Loading Indicator */}
              {loading && <LoadingIndicator />}
            </View>
          )}
        </ScrollView>

        {/* Voice Input Overlay */}
        {isListening && (
          <VoiceInput
            isListening={isListening}
            onStartListening={startVoiceRecognition}
            onStopListening={stopVoiceRecognition}
            voiceText={voiceText}
          />
        )}

        {/* Premium Input Section - Fixed at bottom */}
        <SafeAreaView edges={['bottom']}>
          <PremiumInputSection
            inputText={inputText}
            setInputText={handleInputChange}
            loading={loading}
            isListening={isListening}
            onSendMessage={handleSendPress}
            onStartVoice={startVoiceRecognition}
            onStopVoice={stopVoiceRecognition}
          />
        </SafeAreaView>
      </KeyboardAvoidingView>
    </View>
  );
};

// Component for Quick Questions
interface QuickQuestionProps {
  question: QuickQuestionType;
  onPress: () => void;
  index: number;
}

const QuickQuestion: React.FC<QuickQuestionProps> = ({ question, onPress, index }) => {
  const scale = useSharedValue(1);

  const handlePressIn = () => {
    scale.value = withSpring(0.95);
  };

  const handlePressOut = () => {
    scale.value = withSpring(1);
  };

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  return (
    <Animated.View
      entering={SlideInRight.delay(index * 100).duration(500)}
      style={[styles.quickQuestion, animatedStyle]}
    >
      <TouchableOpacity
        style={styles.quickQuestionButton}
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={1}
      >
        <View style={styles.quickQuestionIcon}>
          <Ionicons name={question.icon as any} size={20} color={Colors.brand} />
        </View>
        <Text style={styles.quickQuestionText}>{question.text}</Text>
        <Ionicons name="chevron-forward" size={16} color={Colors.mutedForeground} />
      </TouchableOpacity>
    </Animated.View>
  );
};

// Component for Message Bubbles
interface MessageBubbleProps {
  message: ChatMessage;
}

const MessageBubble: React.FC<MessageBubbleProps> = ({ message }) => {
  const handleCopyMessage = async () => {
    try {
      await Clipboard.setStringAsync(message.text);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      Alert.alert('Copied!', 'Message copied to clipboard');
    } catch (error) {
      console.error('Error copying message:', error);
      Alert.alert('Error', 'Failed to copy message');
    }
  };

  const handleShareMessage = async () => {
    try {
      await Share.share({
        message: `NutriAI: ${message.text}`,
        title: 'NutriAI Response',
      });
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    } catch (error) {
      console.error('Error sharing message:', error);
    }
  };

  const handleLikeMessage = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    Alert.alert('Thanks!', 'Your feedback helps improve NutriAI');
  };

  const markdownStyles = {
    body: {
      ...Typography.bodyLarge,
      color: message.isUser ? Colors.primaryForeground : Colors.foreground,
      lineHeight: 22,
    },
    paragraph: {
      marginBottom: 8,
    },
    strong: {
      fontWeight: '700' as const,
    },
    em: {
      fontStyle: 'italic' as const,
    },
    list_item: {
      marginBottom: 4,
    },
    bullet_list: {
      marginBottom: 8,
    },
    ordered_list: {
      marginBottom: 8,
    },
  };

  return (
    <Animated.View
      entering={FadeInUp.duration(400)}
      style={[
        styles.messageBubble,
        message.isUser ? styles.userMessage : styles.aiMessage,
      ]}
    >
      {!message.isUser && (
        <View style={styles.aiAvatar}>
          <Image
            source={require('../../assets/image final.png')}
            style={styles.aiAvatarLogo}
            resizeMode="contain"
          />
        </View>
      )}
      <View style={[
        styles.messageContent,
        message.isUser ? styles.userMessageContent : styles.aiMessageContent,
      ]}>
        {message.isUser ? (
          <Text style={[
            styles.messageText,
            styles.userMessageText,
          ]}>
            {message.text}
          </Text>
        ) : (
          <Markdown style={markdownStyles}>
            {message.text}
          </Markdown>
        )}

        <View style={styles.messageFooter}>
          <Text style={styles.messageTime}>
            {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </Text>

          {!message.isUser && (
            <View style={styles.messageActions}>
              <TouchableOpacity
                style={styles.actionButton}
                onPress={handleCopyMessage}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <Ionicons name="copy-outline" size={16} color={Colors.mutedForeground} />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.actionButton}
                onPress={handleShareMessage}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <Ionicons name="share-outline" size={16} color={Colors.mutedForeground} />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.actionButton}
                onPress={handleLikeMessage}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <Ionicons name="heart-outline" size={16} color={Colors.mutedForeground} />
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>
    </Animated.View>
  );
};

// Component for Typing Indicator
interface TypingIndicatorProps {
  text: string;
}

const TypingIndicator: React.FC<TypingIndicatorProps> = ({ text }) => {
  const dotOpacity1 = useSharedValue(0.3);
  const dotOpacity2 = useSharedValue(0.6);
  const dotOpacity3 = useSharedValue(1);

  useEffect(() => {
    const animate = () => {
      dotOpacity1.value = withSequence(
        withTiming(1, { duration: 500 }),
        withTiming(0.3, { duration: 500 })
      );
      dotOpacity2.value = withSequence(
        withDelay(200, withTiming(1, { duration: 500 })),
        withTiming(0.6, { duration: 500 })
      );
      dotOpacity3.value = withSequence(
        withDelay(400, withTiming(1, { duration: 500 })),
        withTiming(1, { duration: 500 })
      );
    };

    const interval = setInterval(animate, 1500);
    animate();

    return () => clearInterval(interval);
  }, []);

  const dot1Style = useAnimatedStyle(() => ({ opacity: dotOpacity1.value }));
  const dot2Style = useAnimatedStyle(() => ({ opacity: dotOpacity2.value }));
  const dot3Style = useAnimatedStyle(() => ({ opacity: dotOpacity3.value }));

  return (
    <Animated.View entering={FadeInUp.duration(300)} style={styles.typingContainer}>
      <View style={styles.aiAvatar}>
        <Image
          source={require('../../assets/image final.png')}
          style={styles.aiAvatarLogo}
          resizeMode="contain"
        />
      </View>
      <View style={styles.typingBubble}>
        <Text style={styles.typingText}>{text}</Text>
        <View style={styles.typingDots}>
          <Animated.View style={[styles.typingDot, dot1Style]} />
          <Animated.View style={[styles.typingDot, dot2Style]} />
          <Animated.View style={[styles.typingDot, dot3Style]} />
        </View>
      </View>
    </Animated.View>
  );
};

// Component for Loading Indicator
const LoadingIndicator: React.FC = () => {
  const rotation = useSharedValue(0);

  useEffect(() => {
    rotation.value = withRepeat(
      withTiming(360, { duration: 1000, easing: Easing.linear }),
      -1,
      false
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${rotation.value}deg` }],
  }));

  return (
    <Animated.View entering={FadeInUp.duration(300)} style={styles.loadingContainer}>
      <View style={styles.aiAvatar}>
        <Image
          source={require('../../assets/image final.png')}
          style={styles.aiAvatarLogo}
          resizeMode="contain"
        />
      </View>
      <View style={styles.loadingBubble}>
        <View style={styles.loadingContent}>
          <Animated.View style={[styles.loadingSpinner, animatedStyle]}>
            <Ionicons name="refresh" size={16} color={Colors.brand} />
          </Animated.View>
          <Text style={styles.loadingText}>Thinking...</Text>
        </View>
      </View>
    </Animated.View>
  );
};

// Component for Voice Input Overlay
interface VoiceInputProps {
  isListening: boolean;
  onStartListening: () => void;
  onStopListening: () => void;
  voiceText: string;
}

const VoiceInput: React.FC<VoiceInputProps> = ({
  isListening,
  onStartListening,
  onStopListening,
  voiceText,
}) => {
  const scale = useSharedValue(1);
  const opacity = useSharedValue(0);

  useEffect(() => {
    if (isListening) {
      opacity.value = withTiming(1, { duration: 300 });
      scale.value = withRepeat(
        withSequence(
          withTiming(1.1, { duration: 800 }),
          withTiming(1, { duration: 800 })
        ),
        -1,
        true
      );
    } else {
      opacity.value = withTiming(0, { duration: 300 });
      scale.value = withTiming(1, { duration: 300 });
    }
  }, [isListening]);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
    transform: [{ scale: scale.value }],
  }));

  const pulseStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  if (!isListening) return null;

  return (
    <Animated.View style={[styles.voiceOverlay, animatedStyle]}>
      <View style={styles.voiceContainer}>
        <Animated.View style={[styles.voicePulse, pulseStyle]} />
        <TouchableOpacity
          style={styles.voiceButton}
          onPress={onStopListening}
          onPressIn={() => Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium)}
        >
          <Ionicons name="stop" size={32} color="#FFFFFF" />
        </TouchableOpacity>

        <Text style={styles.voiceInstructionText}>
          {voiceText || 'Listening... Tap to stop'}
        </Text>
      </View>
    </Animated.View>
  );
};

// Component for Premium Input Section
interface PremiumInputSectionProps {
  inputText: string;
  setInputText: (text: string) => void;
  loading: boolean;
  isListening: boolean;
  onSendMessage: () => void;
  onStartVoice: () => void;
  onStopVoice: () => void;
}

const PremiumInputSection: React.FC<PremiumInputSectionProps> = ({
  inputText,
  setInputText,
  loading,
  isListening,
  onSendMessage,
  onStartVoice,
  onStopVoice,
}) => {
  const hasText = inputText.trim().length > 0;

  return (
    <View style={styles.premiumInputContainer}>
      <View style={styles.premiumInputWrapper}>
        <TextInput
          style={styles.premiumTextInput}
          placeholder="Ask me anything about nutrition..."
          placeholderTextColor={Colors.mutedForeground}
          value={inputText}
          onChangeText={setInputText}
          multiline
          maxLength={500}
          blurOnSubmit={false}
          returnKeyType="send"
          autoCorrect={true}
          autoCapitalize="sentences"
          textAlignVertical="top"
          keyboardType="default"
          textContentType="none"
          autoComplete="off"
          editable={!loading}
        />

        <TouchableOpacity
          onPress={() => {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            isListening ? onStopVoice() : onStartVoice();
          }}
          activeOpacity={0.8}
          style={[styles.premiumVoiceButton, isListening && styles.premiumVoiceButtonActive]}
        >
          <Ionicons
            name={isListening ? "stop" : "mic"}
            size={16}
            color="white"
          />
        </TouchableOpacity>

        {hasText && (
          <TouchableOpacity
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
              onSendMessage();
            }}
            activeOpacity={0.8}
            style={styles.premiumSendButton}
            disabled={loading}
          >
            <Ionicons
              name="send"
              size={16}
              color="white"
            />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  // Premium Container
  premiumContainer: {
    flex: 1,
    backgroundColor: Colors.background,
  },

  // Premium Header
  premiumHeader: {
    backgroundColor: Colors.background,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
    zIndex: 1000,
    ...Shadows.sm,
  },
  premiumHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.xl,
    paddingVertical: Spacing.md,
  },
  premiumHeaderTitleSection: {
    flex: 1,
  },
  premiumHeaderTitle: {
    ...Typography.headingLarge,
    color: Colors.foreground,
    marginBottom: 2,
  },
  premiumHeaderSubtitle: {
    ...Typography.bodyMedium,
    color: Colors.mutedForeground,
  },
  premiumHeaderMenuButton: {
    padding: Spacing.sm,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.muted,
  },

  // Welcome Section
  welcomeSection: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: Spacing.xl,
    paddingVertical: Spacing.xxxl,
  },
  welcomeCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.xxxl,
    padding: Spacing.xxxl,
    alignItems: 'center',
    marginBottom: Spacing.xxxl,
    ...Shadows.md,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  welcomeIcon: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: Colors.background,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: Spacing.xl,
    ...Shadows.sm,
  },
  welcomeIconImage: {
    width: 60,
    height: 24,
  },
  welcomeTitle: {
    ...Typography.headingMedium,
    color: Colors.foreground,
    textAlign: 'center',
    marginBottom: Spacing.md,
  },
  welcomeDescription: {
    ...Typography.bodyLarge,
    color: Colors.mutedForeground,
    textAlign: 'center',
    lineHeight: 24,
  },

  // Quick Questions
  quickQuestionsSection: {
    marginTop: Spacing.xl,
  },
  quickQuestionsTitle: {
    ...Typography.titleLarge,
    color: Colors.foreground,
    marginBottom: Spacing.lg,
    paddingHorizontal: Spacing.sm,
  },
  quickQuestionsGrid: {
    gap: Spacing.md,
  },
  quickQuestion: {
    marginBottom: Spacing.sm,
  },
  quickQuestionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.xl,
    padding: Spacing.lg,
    borderWidth: 1,
    borderColor: Colors.border,
    ...Shadows.sm,
  },
  quickQuestionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.muted,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.md,
  },
  quickQuestionText: {
    flex: 1,
    ...Typography.bodyLarge,
    color: Colors.foreground,
  },

  // Chat Container
  chatContainer: {
    paddingHorizontal: Spacing.xl,
    paddingVertical: Spacing.lg,
    paddingBottom: Spacing.xxxl,
  },

  // Message Bubbles
  messageBubble: {
    flexDirection: 'row',
    marginBottom: Spacing.lg,
    alignItems: 'flex-end',
  },
  userMessage: {
    justifyContent: 'flex-end',
  },
  aiMessage: {
    justifyContent: 'flex-start',
  },
  aiAvatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#FFFFFF',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.sm,
    ...Shadows.sm,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  aiAvatarLogo: {
    width: 20,
    height: 8,
  },
  messageContent: {
    maxWidth: '75%',
    borderRadius: BorderRadius.xl,
    padding: Spacing.md,
    ...Shadows.sm,
  },
  userMessageContent: {
    backgroundColor: Colors.brand,
    borderBottomRightRadius: BorderRadius.sm,
  },
  aiMessageContent: {
    backgroundColor: Colors.card,
    borderBottomLeftRadius: BorderRadius.sm,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  messageText: {
    ...Typography.bodyLarge,
    lineHeight: 22,
    marginBottom: 4,
  },
  userMessageText: {
    color: Colors.primaryForeground,
  },
  aiMessageText: {
    color: Colors.foreground,
  },
  messageTime: {
    ...Typography.labelSmall,
    opacity: 0.7,
  },
  messageFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  messageActions: {
    flexDirection: 'row',
    gap: Spacing.sm,
  },
  actionButton: {
    padding: Spacing.xs,
    borderRadius: BorderRadius.sm,
    backgroundColor: Colors.muted,
  },

  // Typing Indicator
  typingContainer: {
    flexDirection: 'row',
    marginBottom: Spacing.lg,
    alignItems: 'flex-end',
  },
  typingBubble: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.xl,
    borderBottomLeftRadius: BorderRadius.sm,
    padding: Spacing.md,
    maxWidth: '75%',
    borderWidth: 1,
    borderColor: Colors.border,
    ...Shadows.sm,
  },
  typingText: {
    ...Typography.bodyLarge,
    color: Colors.foreground,
    marginBottom: 8,
  },
  typingDots: {
    flexDirection: 'row',
    gap: 4,
    alignItems: 'center',
  },
  typingDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: Colors.brand,
  },

  // Loading Indicator
  loadingContainer: {
    flexDirection: 'row',
    marginBottom: Spacing.lg,
    alignItems: 'flex-end',
  },
  loadingBubble: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.xl,
    borderBottomLeftRadius: BorderRadius.sm,
    padding: Spacing.md,
    maxWidth: '75%',
    borderWidth: 1,
    borderColor: Colors.border,
    ...Shadows.sm,
  },
  loadingContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },
  loadingSpinner: {
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    ...Typography.bodyLarge,
    color: Colors.foreground,
  },

  // Voice Input Overlay
  voiceOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1000,
  },
  voiceContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  voicePulse: {
    position: 'absolute',
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: 'rgba(107, 124, 90, 0.3)',
  },
  voiceButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: Colors.brand,
    alignItems: 'center',
    justifyContent: 'center',
    ...Shadows.lg,
  },
  voiceInstructionText: {
    ...Typography.bodyLarge,
    color: Colors.primaryForeground,
    textAlign: 'center',
    marginTop: Spacing.xl,
    paddingHorizontal: Spacing.xl,
  },

  // Premium Input Section
  premiumInputContainer: {
    backgroundColor: Colors.background,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    paddingHorizontal: Spacing.xl,
    paddingVertical: Spacing.lg,
    ...Shadows.sm,
  },
  premiumInputWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.xxxl,
    borderWidth: 1,
    borderColor: Colors.border,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    minHeight: 52,
    ...Shadows.sm,
  },
  premiumTextInput: {
    flex: 1,
    ...Typography.bodyLarge,
    color: Colors.foreground,
    lineHeight: 22,
    maxHeight: 120,
    paddingVertical: Spacing.xs,
    paddingRight: Spacing.sm,
  },
  premiumVoiceButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: Colors.mutedForeground,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: Spacing.sm,
    ...Shadows.sm,
  },
  premiumVoiceButtonActive: {
    backgroundColor: Colors.destructive,
  },
  premiumSendButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: Colors.brand,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: Spacing.sm,
    ...Shadows.sm,
  },
});

export default AskScreenModern;