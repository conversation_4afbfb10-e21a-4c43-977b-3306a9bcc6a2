import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Platform,
  Alert,
  StatusBar,
  KeyboardAvoidingView,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import Animated, {
  FadeInUp,
  FadeInDown,
  SlideInRight,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
} from 'react-native-reanimated';

// Import services
import ApiService from '../services/ApiService';
import VoiceService from '../services/VoiceService';

// Import theme
import { Colors, FontSizes, Spacing, BorderRadius } from '../theme/theme';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface ChatMessage {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
}

interface QuickQuestionType {
  id: string;
  text: string;
  icon: string;
}

const quickQuestions: QuickQuestionType[] = [
  { id: '1', text: 'What are the best sources of protein?', icon: 'fitness' },
  { id: '2', text: 'How much water should I drink daily?', icon: 'water' },
  { id: '3', text: 'What foods boost metabolism?', icon: 'flash' },
  { id: '4', text: 'Best foods for weight loss?', icon: 'trending-down' },
];

const AskScreenModern: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputText, setInputText] = useState('');
  const [loading, setLoading] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [voiceText, setVoiceText] = useState('');
  const [typingText, setTypingText] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  
  const scrollViewRef = useRef<ScrollView>(null);
  const textInputRef = useRef<TextInput>(null);

  // Animation values
  const scrollY = useSharedValue(0);
  const headerOpacity = useSharedValue(1);

  useEffect(() => {
    // Initialize voice service
    VoiceService.initialize();
  }, []);

  // Simple typing animation
  useEffect(() => {
    if (isTyping && typingText) {
      let currentIndex = 0;
      const interval = setInterval(() => {
        if (currentIndex < typingText.length) {
          currentIndex += 2;
        } else {
          clearInterval(interval);
          // Add complete message
          const aiMessage: ChatMessage = {
            id: Date.now().toString(),
            text: typingText,
            isUser: false,
            timestamp: new Date(),
          };
          setMessages(prev => [...prev, aiMessage]);
          setIsTyping(false);
          setTypingText('');
          
          // Scroll to bottom
          setTimeout(() => {
            scrollViewRef.current?.scrollToEnd({ animated: true });
          }, 100);
        }
      }, 30);

      return () => clearInterval(interval);
    }
  }, [isTyping, typingText]);

  const sendMessage = async (text: string) => {
    if (!text.trim()) return;

    // Add user message
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      text: text.trim(),
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setLoading(true);

    // Scroll to show user message
    setTimeout(() => {
      scrollViewRef.current?.scrollToEnd({ animated: true });
    }, 100);

    try {
      const responseText = await ApiService.askNutritionQuestion(text);
      setLoading(false);
      
      // Start typing animation
      setTypingText(responseText);
      setIsTyping(true);
    } catch (error) {
      console.error('Error asking question:', error);
      setLoading(false);
      
      // Fallback response
      const fallbackText = "I'm here to help with your nutrition questions! While I'm currently having trouble connecting to my knowledge base, I'd recommend consulting with a registered dietitian for personalized advice.";
      setTypingText(fallbackText);
      setIsTyping(true);
    }
  };

  const handleQuickQuestion = (question: string) => {
    sendMessage(question);
  };

  const startVoiceRecognition = async () => {
    try {
      const started = await VoiceService.startListening({
        onStart: () => {
          setIsListening(true);
          setVoiceText('');
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        },
        onResult: (result) => {
          if (result.isFinal && result.text.trim()) {
            setVoiceText(result.text);
            setInputText(result.text);
            setIsListening(false);
            Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          }
        },
        onPartialResult: (text) => {
          setVoiceText(text);
        },
        onEnd: () => {
          setIsListening(false);
        },
        onError: (error) => {
          setIsListening(false);
          setVoiceText('');
          Alert.alert('Voice Recognition Error', error);
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
        }
      });

      if (!started) {
        Alert.alert('Voice Recognition', 'Voice recognition is not available on this device.');
      }
    } catch (error) {
      console.error('Error starting voice recognition:', error);
      Alert.alert('Error', 'Failed to start voice recognition');
    }
  };

  const stopVoiceRecognition = async () => {
    try {
      await VoiceService.stopListening();
      setIsListening(false);
    } catch (error) {
      console.error('Error stopping voice recognition:', error);
    }
  };

  const handleSendPress = () => {
    if (inputText.trim()) {
      sendMessage(inputText);
    }
  };

  const headerStyle = useAnimatedStyle(() => ({
    opacity: headerOpacity.value,
    transform: [{ translateY: interpolate(scrollY.value, [0, 100], [0, -10], 'clamp') }],
  }));

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />
      
      {/* Header */}
      <Animated.View style={[styles.header, headerStyle]} entering={FadeInDown.duration(600)}>
        <SafeAreaView edges={['top']}>
          <View style={styles.headerContent}>
            <View style={styles.headerTitleSection}>
              <Text style={styles.headerTitle}>Ask AI</Text>
              <Text style={styles.headerSubtitle}>Nutrition Assistant 🍃</Text>
            </View>
            <TouchableOpacity
              style={styles.menuButton}
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                Alert.alert(
                  'Menu Options',
                  'Choose an action',
                  [
                    { text: 'Clear Chat', onPress: () => setMessages([]) },
                    { text: 'Cancel', style: 'cancel' },
                  ]
                );
              }}
            >
              <Ionicons name="ellipsis-horizontal" size={24} color="#6B7C5A" />
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      </Animated.View>

      <KeyboardAvoidingView 
        style={{ flex: 1 }} 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        {/* Messages Container */}
        <ScrollView
          ref={scrollViewRef}
          style={styles.messagesContainer}
          contentContainerStyle={styles.messagesContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {messages.length === 0 ? (
            // Welcome Screen
            <Animated.View entering={FadeInUp.delay(300).duration(800)} style={styles.welcomeSection}>
              <View style={styles.welcomeCard}>
                <View style={styles.welcomeIcon}>
                  <Ionicons name="chatbubble-ellipses" size={40} color={Colors.brand} />
                </View>
                <Text style={styles.welcomeTitle}>Welcome to AI Nutritionist!</Text>
                <Text style={styles.welcomeDescription}>
                  Ask me anything about nutrition, diet, and healthy eating. I'm here to help you make informed decisions about your health.
                </Text>
              </View>

              <View style={styles.quickQuestionsSection}>
                <Text style={styles.quickQuestionsTitle}>Popular Questions</Text>
                <View style={styles.quickQuestionsGrid}>
                  {quickQuestions.map((question, index) => (
                    <QuickQuestion
                      key={question.id}
                      question={question}
                      onPress={() => handleQuickQuestion(question.text)}
                      index={index}
                    />
                  ))}
                </View>
              </View>
            </Animated.View>
          ) : (
            // Chat Messages
            <View style={styles.chatContainer}>
              {messages.map((message) => (
                <MessageBubble key={message.id} message={message} />
              ))}
              
              {/* Typing Indicator */}
              {isTyping && (
                <TypingIndicator text={typingText.substring(0, Math.floor(typingText.length * 0.8))} />
              )}
              
              {/* Loading Indicator */}
              {loading && <LoadingIndicator />}
            </View>
          )}
        </ScrollView>

        {/* Input Section */}
        <SafeAreaView edges={['bottom']}>
          <InputSection
            inputText={inputText}
            setInputText={setInputText}
            loading={loading}
            isListening={isListening}
            onSendMessage={handleSendPress}
            onStartVoice={startVoiceRecognition}
            onStopVoice={stopVoiceRecognition}
            textInputRef={textInputRef}
          />
        </SafeAreaView>
      </KeyboardAvoidingView>
    </View>
  );
};

// Component for Quick Questions
interface QuickQuestionProps {
  question: QuickQuestionType;
  onPress: () => void;
  index: number;
}

const QuickQuestion: React.FC<QuickQuestionProps> = ({ question, onPress, index }) => {
  const scale = useSharedValue(1);

  const handlePressIn = () => {
    scale.value = withSpring(0.95);
  };

  const handlePressOut = () => {
    scale.value = withSpring(1);
  };

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  return (
    <Animated.View
      entering={SlideInRight.delay(index * 100).duration(500)}
      style={[styles.quickQuestion, animatedStyle]}
    >
      <TouchableOpacity
        style={styles.quickQuestionButton}
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={1}
      >
        <View style={styles.quickQuestionIcon}>
          <Ionicons name={question.icon as any} size={20} color={Colors.brand} />
        </View>
        <Text style={styles.quickQuestionText}>{question.text}</Text>
        <Ionicons name="chevron-forward" size={16} color="#9CA3AF" />
      </TouchableOpacity>
    </Animated.View>
  );
};

// Component for Message Bubbles
interface MessageBubbleProps {
  message: ChatMessage;
}

const MessageBubble: React.FC<MessageBubbleProps> = ({ message }) => {
  return (
    <Animated.View
      entering={FadeInUp.duration(400)}
      style={[
        styles.messageBubble,
        message.isUser ? styles.userMessage : styles.aiMessage,
      ]}
    >
      {!message.isUser && (
        <View style={styles.aiAvatar}>
          <Ionicons name="nutrition" size={16} color="#FFFFFF" />
        </View>
      )}
      <View style={[
        styles.messageContent,
        message.isUser ? styles.userMessageContent : styles.aiMessageContent,
      ]}>
        <Text style={[
          styles.messageText,
          message.isUser ? styles.userMessageText : styles.aiMessageText,
        ]}>
          {message.text}
        </Text>
        <Text style={styles.messageTime}>
          {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
        </Text>
      </View>
    </Animated.View>
  );
};

// Component for Typing Indicator
interface TypingIndicatorProps {
  text: string;
}

const TypingIndicator: React.FC<TypingIndicatorProps> = ({ text }) => {
  return (
    <Animated.View entering={FadeInUp.duration(300)} style={styles.typingContainer}>
      <View style={styles.aiAvatar}>
        <Ionicons name="nutrition" size={16} color="#FFFFFF" />
      </View>
      <View style={styles.typingBubble}>
        <Text style={styles.typingText}>{text}</Text>
        <View style={styles.typingDots}>
          <Animated.View style={[styles.typingDot, { opacity: withTiming(0.3, { duration: 500 }) }]} />
          <Animated.View style={[styles.typingDot, { opacity: withTiming(0.6, { duration: 500 }) }]} />
          <Animated.View style={[styles.typingDot, { opacity: withTiming(1, { duration: 500 }) }]} />
        </View>
      </View>
    </Animated.View>
  );
};

// Component for Loading Indicator
const LoadingIndicator: React.FC = () => {
  return (
    <Animated.View entering={FadeInUp.duration(300)} style={styles.loadingContainer}>
      <View style={styles.aiAvatar}>
        <Ionicons name="nutrition" size={16} color="#FFFFFF" />
      </View>
      <View style={styles.loadingBubble}>
        <Text style={styles.loadingText}>Thinking...</Text>
        <View style={styles.loadingDots}>
          <Animated.View style={[styles.loadingDot, { opacity: withTiming(1, { duration: 500 }) }]} />
          <Animated.View style={[styles.loadingDot, { opacity: withTiming(0.6, { duration: 500 }) }]} />
          <Animated.View style={[styles.loadingDot, { opacity: withTiming(0.3, { duration: 500 }) }]} />
        </View>
      </View>
    </Animated.View>
  );
};

// Component for Input Section
interface InputSectionProps {
  inputText: string;
  setInputText: (text: string) => void;
  loading: boolean;
  isListening: boolean;
  onSendMessage: () => void;
  onStartVoice: () => void;
  onStopVoice: () => void;
  textInputRef: React.RefObject<TextInput>;
}

const InputSection: React.FC<InputSectionProps> = ({
  inputText,
  setInputText,
  loading,
  isListening,
  onSendMessage,
  onStartVoice,
  onStopVoice,
  textInputRef,
}) => {
  const hasText = inputText.trim().length > 0;

  return (
    <View style={styles.inputContainer}>
      <View style={styles.inputWrapper}>
        <TextInput
          ref={textInputRef}
          style={styles.textInput}
          placeholder="Ask me anything about nutrition..."
          placeholderTextColor="#9CA3AF"
          value={inputText}
          onChangeText={setInputText}
          multiline
          maxLength={500}
          blurOnSubmit={false}
          returnKeyType="send"
          autoCorrect={true}
          autoCapitalize="sentences"
          textAlignVertical="top"
          keyboardType="default"
          editable={!loading}
        />

        <TouchableOpacity
          onPress={() => {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            isListening ? onStopVoice() : onStartVoice();
          }}
          activeOpacity={0.8}
          style={[styles.voiceButton, isListening && styles.voiceButtonActive]}
        >
          <Ionicons
            name={isListening ? "stop" : "mic"}
            size={16}
            color="white"
          />
        </TouchableOpacity>

        {hasText && (
          <TouchableOpacity
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
              onSendMessage();
            }}
            activeOpacity={0.8}
            style={styles.sendButton}
            disabled={loading}
          >
            <Ionicons
              name="send"
              size={16}
              color="white"
            />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
    zIndex: 1000,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
  },
  headerTitleSection: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 2,
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  menuButton: {
    padding: Spacing.sm,
    borderRadius: BorderRadius.md,
    backgroundColor: '#F9FAFB',
  },
  messagesContainer: {
    flex: 1,
  },
  messagesContent: {
    flexGrow: 1,
    paddingHorizontal: Spacing.lg,
    paddingTop: Spacing.md,
  },
  welcomeSection: {
    flex: 1,
    justifyContent: 'center',
    paddingVertical: Spacing.xl,
  },
  welcomeCard: {
    backgroundColor: '#F8FAFC',
    borderRadius: BorderRadius.xl,
    padding: Spacing.xl,
    alignItems: 'center',
    marginBottom: Spacing.xl,
  },
  welcomeIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#FFFFFF',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: Spacing.lg,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  welcomeTitle: {
    fontSize: 22,
    fontWeight: '700',
    color: '#1F2937',
    textAlign: 'center',
    marginBottom: Spacing.sm,
  },
  welcomeDescription: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
  },
  quickQuestionsSection: {
    marginTop: Spacing.lg,
  },
  quickQuestionsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: Spacing.md,
  },
  quickQuestionsGrid: {
    gap: Spacing.sm,
  },
  quickQuestion: {
    marginBottom: Spacing.sm,
  },
  quickQuestionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  quickQuestionIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F0FDF4',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.sm,
  },
  quickQuestionText: {
    flex: 1,
    fontSize: 15,
    color: '#374151',
    fontWeight: '500',
  },
  chatContainer: {
    paddingBottom: Spacing.lg,
  },
  messageBubble: {
    flexDirection: 'row',
    marginBottom: Spacing.md,
    alignItems: 'flex-end',
  },
  userMessage: {
    justifyContent: 'flex-end',
  },
  aiMessage: {
    justifyContent: 'flex-start',
  },
  aiAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.brand,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.sm,
  },
  messageContent: {
    maxWidth: '75%',
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
  },
  userMessageContent: {
    backgroundColor: Colors.brand,
    borderBottomRightRadius: 4,
  },
  aiMessageContent: {
    backgroundColor: '#F3F4F6',
    borderBottomLeftRadius: 4,
  },
  messageText: {
    fontSize: 15,
    lineHeight: 20,
    marginBottom: 4,
  },
  userMessageText: {
    color: '#FFFFFF',
  },
  aiMessageText: {
    color: '#1F2937',
  },
  messageTime: {
    fontSize: 11,
    opacity: 0.7,
  },
  typingContainer: {
    flexDirection: 'row',
    marginBottom: Spacing.md,
    alignItems: 'flex-end',
  },
  typingBubble: {
    backgroundColor: '#F3F4F6',
    borderRadius: BorderRadius.lg,
    borderBottomLeftRadius: 4,
    padding: Spacing.md,
    maxWidth: '75%',
  },
  typingText: {
    fontSize: 15,
    color: '#1F2937',
    marginBottom: 4,
  },
  typingDots: {
    flexDirection: 'row',
    gap: 4,
  },
  typingDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#9CA3AF',
  },
  loadingContainer: {
    flexDirection: 'row',
    marginBottom: Spacing.md,
    alignItems: 'flex-end',
  },
  loadingBubble: {
    backgroundColor: '#F3F4F6',
    borderRadius: BorderRadius.lg,
    borderBottomLeftRadius: 4,
    padding: Spacing.md,
    maxWidth: '75%',
  },
  loadingText: {
    fontSize: 15,
    color: '#1F2937',
    marginBottom: 4,
  },
  loadingDots: {
    flexDirection: 'row',
    gap: 4,
  },
  loadingDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#9CA3AF',
  },
  inputContainer: {
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    backgroundColor: '#F9FAFB',
    borderRadius: BorderRadius.xl,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    minHeight: 48,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: '#1F2937',
    lineHeight: 22,
    maxHeight: 120,
    paddingVertical: Spacing.xs,
    paddingRight: Spacing.sm,
  },
  voiceButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#6B7280',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: Spacing.xs,
  },
  voiceButtonActive: {
    backgroundColor: '#EF4444',
  },
  sendButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.brand,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: Spacing.xs,
  },
});

export default AskScreenModern;
