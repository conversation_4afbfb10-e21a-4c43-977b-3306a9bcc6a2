import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Share,
  Dimensions,
  StatusBar,
  ImageBackground,
  Pressable,
} from 'react-native';
import * as Haptics from 'expo-haptics';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import FavoritesService from '../services/FavoritesService';
import Animated, {
  FadeInUp,
  FadeInDown,
  FadeInLeft,
  FadeInRight,
  SlideInUp,
  SlideInDown,
  SlideInLeft,
  SlideInRight,
  ZoomIn,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
  runOnJS,
  withSequence,
  withDelay,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { useRoute, useNavigation } from '@react-navigation/native';

import { Colors, Spacing, BorderRadius, FontSizes, FontWeights, Shadows } from '../constants/Colors';
import { ModernCard } from '../components/ModernCard';
import { ModernButton } from '../components/ModernButton';
import { ModernModal } from '../components/ModernModal';
import { ModernLoading } from '../components/ModernLoading';

const { width, height } = Dimensions.get('window');

interface Recipe {
  id: string;
  title: string;
  description: string;
  cookTime: string;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  calories: number;
  ingredients: string[];
  instructions: string[];
  tags: string[];
  image?: string;
  imageUrl?: string; // Unsplash image URL
  rating?: number;
  servings?: number;
  prepTime?: string;
  nutrition?: {
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
  };
}

interface IngredientItemProps {
  ingredient: string;
  servings: number;
  originalServings: number;
  index: number;
}

interface InstructionStepProps {
  instruction: string;
  stepNumber: number;
  isActive: boolean;
  isCompleted: boolean;
  onPress: () => void;
  onTimerPress: () => void;
  index: number;
}

interface NutritionRingProps {
  label: string;
  value: number;
  maxValue: number;
  color: string;
  unit: string;
  index: number;
}

// Modern Ingredient Item Component
const IngredientItem: React.FC<IngredientItemProps> = ({
  ingredient,
  servings,
  originalServings,
  index,
}) => {
  const scale = useSharedValue(1);
  const opacity = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  const handlePress = () => {
    scale.value = withSequence(
      withTiming(0.95, { duration: 100 }),
      withTiming(1, { duration: 100 })
    );
    opacity.value = withSequence(
      withTiming(0.7, { duration: 100 }),
      withTiming(1, { duration: 100 })
    );
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const scaleIngredient = (ingredient: string) => {
    if (servings === originalServings) return ingredient;
    
    const scaleFactor = servings / originalServings;
    return ingredient.replace(/(\d+(?:\.\d+)?)\s*(\w+)/g, (match, amount, unit) => {
      const scaledAmount = (parseFloat(amount) * scaleFactor).toFixed(1);
      return `${scaledAmount} ${unit}`;
    });
  };

  return (
    <Animated.View
      entering={SlideInLeft.delay(index * 100).duration(500)}
      style={[styles.modernIngredientItem, animatedStyle]}
    >
      <Pressable style={styles.modernIngredientButton} onPress={handlePress}>
        <View style={styles.ingredientIcon}>
          <Ionicons name="nutrition" size={16} color={Colors.brand} />
        </View>
        <Text style={styles.modernIngredientText}>
          {scaleIngredient(ingredient)}
        </Text>
      </Pressable>
    </Animated.View>
  );
};

// Premium Timeline Step Component
const PremiumTimelineStep: React.FC<InstructionStepProps> = ({
  instruction,
  stepNumber,
  isActive,
  isCompleted,
  onPress,
  onTimerPress,
  index,
}) => {
  const scale = useSharedValue(1);
  const progressWidth = useSharedValue(0);
  const glowOpacity = useSharedValue(0);
  const checkmarkScale = useSharedValue(0);

  React.useEffect(() => {
    if (isCompleted) {
      checkmarkScale.value = withSequence(
        withTiming(1.2, { duration: 200 }),
        withTiming(1, { duration: 200 })
      );
    }

    if (isActive) {
      glowOpacity.value = withTiming(1, { duration: 300 });
      progressWidth.value = withTiming(100, { duration: 800 });
    } else {
      glowOpacity.value = withTiming(0, { duration: 300 });
      progressWidth.value = withTiming(0, { duration: 300 });
    }
  }, [isActive, isCompleted]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const glowStyle = useAnimatedStyle(() => ({
    opacity: glowOpacity.value,
  }));

  const progressStyle = useAnimatedStyle(() => ({
    width: `${progressWidth.value}%`,
  }));

  const checkmarkStyle = useAnimatedStyle(() => ({
    transform: [{ scale: checkmarkScale.value }],
  }));

  const handlePress = () => {
    scale.value = withSequence(
      withTiming(0.98, { duration: 100 }),
      withTiming(1, { duration: 100 })
    );
    runOnJS(onPress)();
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
  };

  const hasTimer = instruction.toLowerCase().includes('minute') ||
                   instruction.toLowerCase().includes('hour') ||
                   instruction.toLowerCase().includes('cook') ||
                   instruction.toLowerCase().includes('bake');

  const parseInstruction = (text: string) => {
    const sentences = text.split('.').filter(s => s.trim().length > 0);
    if (sentences.length === 1) {
      return { main: text, substeps: [] };
    }
    const main = sentences[0] + '.';
    const substeps = sentences.slice(1).map(s => s.trim()).filter(s => s.length > 0);
    return { main, substeps };
  };

  const { main, substeps } = parseInstruction(instruction);

  return (
    <Animated.View
      entering={SlideInRight.delay(index * 100).duration(500)}
      style={[
        styles.premiumTimelineStep,
        isActive && styles.premiumTimelineStepActive,
        isCompleted && styles.premiumTimelineStepCompleted,
        animatedStyle
      ]}
    >
      <TouchableOpacity
        onPress={handlePress}
        activeOpacity={0.95}
        style={{ flex: 1 }}
      >
        {/* Step Header */}
        <View style={styles.premiumStepHeader}>
          <View style={styles.premiumStepMeta}>
            <View style={[
              styles.premiumStepBadge,
              isActive && styles.premiumStepBadgeActive,
              isCompleted && styles.premiumStepBadgeCompleted
            ]}>
              {isCompleted ? (
                <Animated.View style={checkmarkStyle}>
                  <Ionicons name="checkmark" size={16} color="white" />
                </Animated.View>
              ) : (
                <Text style={[
                  styles.premiumStepBadgeText,
                  isActive && styles.premiumStepBadgeTextActive
                ]}>
                  {stepNumber}
                </Text>
              )}
            </View>

            <Text style={[
              styles.premiumStepNumber,
              isActive && styles.premiumStepNumberActive,
              isCompleted && styles.premiumStepNumberCompleted
            ]}>
              Step {stepNumber}
            </Text>

            {hasTimer && (
              <TouchableOpacity
                style={styles.premiumTimerChip}
                onPress={() => onTimerPress()}
              >
                <Ionicons name="timer-outline" size={14} color="#6B7C5A" />
                <Text style={styles.premiumTimerText}>Timer</Text>
              </TouchableOpacity>
            )}
          </View>

          {isCompleted && (
            <View style={styles.premiumCompletedBadge}>
              <Ionicons name="checkmark-circle" size={20} color="#22C55E" />
            </View>
          )}
        </View>

        {/* Step Instruction */}
        <Text style={[
          styles.premiumStepInstruction,
          isActive && styles.premiumStepInstructionActive,
          isCompleted && styles.premiumStepInstructionCompleted
        ]}>
          {main}
        </Text>

        {/* Sub-steps */}
        {substeps.length > 0 && (
          <View style={styles.premiumSubstepsContainer}>
            {substeps.map((substep, idx) => (
              <View key={idx} style={styles.premiumSubstepItem}>
                <View style={styles.premiumSubstepIndicator} />
                <Text style={[
                  styles.premiumSubstepText,
                  isActive && styles.premiumSubstepTextActive,
                  isCompleted && styles.premiumSubstepTextCompleted
                ]}>
                  {substep}
                </Text>
              </View>
            ))}
          </View>
        )}

        {/* Progress Bar */}
        {isActive && (
          <View style={styles.premiumProgressBar}>
            <Animated.View style={[styles.premiumProgressBarFill, progressStyle]} />
          </View>
        )}
      </TouchableOpacity>
    </Animated.View>
  );
};

// Nutrition Ring Component
const NutritionRing: React.FC<NutritionRingProps> = ({
  label,
  value,
  maxValue,
  color,
  unit,
  index,
}) => {
  const progress = useSharedValue(0);

  useEffect(() => {
    progress.value = withDelay(index * 200, withTiming(value / maxValue, { duration: 1000 }));
  }, [value, maxValue, index]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${progress.value * 360}deg` }],
  }));

  return (
    <Animated.View 
      entering={ZoomIn.delay(index * 100).duration(600)}
      style={styles.nutritionRing}
    >
      <View style={styles.ringContainer}>
        <View style={[styles.ringBackground, { borderColor: color + '20' }]} />
        <Animated.View 
          style={[
            styles.ringProgress, 
            { borderColor: color },
            animatedStyle
          ]} 
        />
        <View style={styles.ringCenter}>
          <Text style={[styles.ringValue, { color }]}>{value}</Text>
          <Text style={styles.ringUnit}>{unit}</Text>
        </View>
      </View>
      <Text style={styles.ringLabel}>{label}</Text>
    </Animated.View>
  );
};

const RecipeDetailScreenModern: React.FC = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const recipe = (route.params as any)?.recipe as Recipe;

  const [servings, setServings] = useState(recipe?.servings || 4);
  const [activeStep, setActiveStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<boolean[]>(
    new Array(recipe?.instructions.length || 0).fill(false)
  );
  const [showNutrition, setShowNutrition] = useState(false);
  const [showTimer, setShowTimer] = useState(false);
  const [timerStep, setTimerStep] = useState(0);
  const [isSaved, setIsSaved] = useState(false);

  const scrollViewRef = useRef<ScrollView>(null);

  // Load favorite status on component mount
  useEffect(() => {
    const loadFavoriteStatus = async () => {
      if (recipe?.id) {
        const favorited = await FavoritesService.isFavorited(recipe.id);
        setIsSaved(favorited);
      }
    };

    loadFavoriteStatus();
  }, [recipe?.id]);

  // Handle favorite toggle
  const handleFavoriteToggle = async () => {
    if (!recipe) return;

    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      const favoriteRecipe = {
        id: recipe.id,
        name: recipe.title || recipe.id, // Use title or fallback to id
        imageUrl: recipe.imageUrl || recipe.image,
        cookingTime: recipe.cookTime,
        difficulty: recipe.difficulty,
        calories: recipe.nutrition?.protein || 0, // Use available nutrition data
        protein: recipe.nutrition?.protein,
        source: 'manual' as const
      };

      const success = await FavoritesService.toggleFavorite(favoriteRecipe);

      if (success) {
        const newStatus = await FavoritesService.isFavorited(recipe.id);
        setIsSaved(newStatus);
        console.log(`${newStatus ? '❤️ Added to' : '💔 Removed from'} favorites: ${recipe.title || recipe.id}`);
      }
    } catch (error) {
      console.error('❌ Error toggling favorite:', error);
    }
  };

  if (!recipe) {
    return (
      <View style={styles.container}>
        <Text>Recipe not found</Text>
      </View>
    );
  }

  // Debug logging
  console.log('Recipe data:', recipe);
  console.log('Recipe image:', recipe.imageUrl || recipe.image);

  // Removed ingredient toggle functionality

  const handleStepPress = (index: number) => {
    setActiveStep(index);
    const newCompleted = [...completedSteps];
    newCompleted[index] = !newCompleted[index];
    setCompletedSteps(newCompleted);
  };

  const handleTimerPress = (stepIndex: number) => {
    setTimerStep(stepIndex);
    setShowTimer(true);
  };

  const startCooking = () => {
    (navigation as any).navigate('CookingTimer', {
      recipe: recipe.title,
      instructions: recipe.instructions,
      servings
    });
  };

  const shareRecipe = async () => {
    try {
      await Share.share({
        message: `Check out this amazing recipe: ${recipe.title}\n\n${recipe.description}`,
        title: recipe.title,
      });
    } catch (error) {
      console.error('Error sharing recipe:', error);
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Easy': return '#6B7C5A';
      case 'Medium': return '#6B7C5A';
      case 'Hard': return '#6B7C5A';
      default: return '#6B7C5A';
    }
  };

  const getCompletionPercentage = () => {
    const completed = completedSteps.filter(Boolean).length;
    return Math.round((completed / completedSteps.length) * 100);
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent={true} />

      {/* Everything Inside ScrollView for Natural Flow */}
      <ScrollView
        ref={scrollViewRef}
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Apple-Inspired Natural Header */}
        <Animated.View entering={FadeInDown.duration(600)} style={styles.appleNaturalHeader}>
          <TouchableOpacity
            style={styles.appleBackButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={20} color="#1D1D1F" />
          </TouchableOpacity>

          <View style={styles.appleHeaderActions}>
            <Pressable
              style={({ pressed }) => [
                styles.appleActionButton,
                isSaved && styles.appleActionButtonActive,
                pressed && styles.appleActionButtonPressed
              ]}
              onPress={handleFavoriteToggle}
            >
              <Ionicons
                name={isSaved ? "heart" : "heart-outline"}
                size={20}
                color={isSaved ? "#6B7C5A" : "#6B7C5A"}
              />
            </Pressable>

            <Pressable
              style={({ pressed }) => [
                styles.appleActionButton,
                pressed && styles.appleActionButtonPressed
              ]}
              onPress={shareRecipe}
            >
              <Ionicons name="share" size={20} color="#6B7C5A" />
            </Pressable>
          </View>
        </Animated.View>

        {/* Apple-Inspired Hero Section */}
        <Animated.View entering={FadeInUp.delay(100).duration(800)} style={styles.appleHeroSection}>
          {/* Beautiful Recipe Image */}
          <View style={styles.appleImageContainer}>
            <ImageBackground
              source={{ uri: recipe.imageUrl || recipe.image || 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80' }}
              style={styles.appleRecipeImage}
              imageStyle={styles.appleImageStyle}
            >
              <LinearGradient
                colors={['transparent', 'rgba(107, 124, 90, 0.1)', 'rgba(107, 124, 90, 0.8)']}
                style={styles.appleImageGradient}
              >
                <View style={styles.appleImageContent}>
                  <Text style={styles.appleRecipeTitle}>{recipe.title}</Text>
                  <Text style={styles.appleRecipeSubtitle}>{recipe.description}</Text>
                </View>
              </LinearGradient>
            </ImageBackground>
          </View>

          {/* Elegant Stats Cards */}
          <Animated.View entering={FadeInUp.delay(300).duration(600)} style={styles.appleStatsContainer}>
            <View style={styles.appleStatCard}>
              <View style={styles.appleStatIcon}>
                <Ionicons name="time" size={18} color="#6B7C5A" />
              </View>
              <Text style={styles.appleStatLabel}>Cook Time</Text>
              <Text style={styles.appleStatValue}>{recipe.cookTime}</Text>
            </View>

            <View style={styles.appleStatCard}>
              <View style={styles.appleStatIcon}>
                <Ionicons name="flame" size={18} color="#6B7C5A" />
              </View>
              <Text style={styles.appleStatLabel}>Calories</Text>
              <Text style={styles.appleStatValue}>{recipe.calories}</Text>
            </View>

            <View style={styles.appleStatCard}>
              <View style={styles.appleStatIcon}>
                <Ionicons name="people" size={18} color="#6B7C5A" />
              </View>
              <Text style={styles.appleStatLabel}>Servings</Text>
              <Text style={styles.appleStatValue}>{servings}</Text>
            </View>
          </Animated.View>
        </Animated.View>
        {/* Beautiful Servings Adjuster */}
        <Animated.View entering={SlideInUp.delay(200).duration(600)} style={styles.modernServingsSection}>
          <View style={styles.modernServingsCard}>
            <View style={styles.modernServingsHeader}>
              <Text style={styles.modernServingsTitle}>Servings</Text>
              <Text style={styles.modernServingsSubtitle}>Adjust recipe quantities</Text>
            </View>

            <View style={styles.modernServingsControls}>
              <TouchableOpacity
                style={[styles.modernServingsButton, servings <= 1 && styles.modernServingsButtonDisabled]}
                onPress={() => servings > 1 && setServings(servings - 1)}
                disabled={servings <= 1}
              >
                <Ionicons name="remove" size={20} color={servings <= 1 ? "#E5E7EB" : "white"} />
              </TouchableOpacity>

              <View style={styles.modernServingsDisplay}>
                <Text style={styles.modernServingsNumber}>{servings}</Text>
                <Text style={styles.modernServingsLabel}>people</Text>
              </View>

              <TouchableOpacity
                style={[styles.modernServingsButton, servings >= 12 && styles.modernServingsButtonDisabled]}
                onPress={() => servings < 12 && setServings(servings + 1)}
                disabled={servings >= 12}
              >
                <Ionicons name="add" size={20} color={servings >= 12 ? "#E5E7EB" : "white"} />
              </TouchableOpacity>
            </View>
          </View>
        </Animated.View>

        {/* Beautiful Ingredients Section */}
        <Animated.View entering={SlideInUp.delay(300).duration(600)} style={styles.modernSection}>
          <View style={styles.modernSectionHeader}>
            <Text style={styles.modernSectionTitle}>Ingredients</Text>
            <View style={styles.modernProgressIndicator}>
              <Text style={styles.modernProgressText}>
                {recipe.ingredients.length} ingredients
              </Text>
            </View>
          </View>

          <View style={styles.modernIngredientsCard}>
            {recipe.ingredients.map((ingredient, index) => (
              <IngredientItem
                key={index}
                ingredient={ingredient}
                servings={servings}
                originalServings={recipe.servings || 4}
                index={index}
              />
            ))}
          </View>
        </Animated.View>

        {/* Premium Cooking Timeline */}
        <Animated.View entering={SlideInUp.delay(400).duration(600)} style={styles.premiumTimelineSection}>
          <View style={styles.premiumTimelineHeader}>
            <View style={styles.premiumTimelineHeaderText}>
              <Text style={styles.premiumTimelineTitle}>Cooking Timeline</Text>
              <Text style={styles.premiumTimelineSubtitle}>Follow the steps to create your masterpiece</Text>

              <View style={styles.premiumProgressContainer}>
                <View style={styles.premiumProgressRing}>
                  <Text style={styles.premiumProgressText}>{getCompletionPercentage()}%</Text>
                </View>
              </View>
            </View>
          </View>

          <View style={styles.premiumTimelineContainer}>
            {recipe.instructions.map((instruction, index) => (
              <PremiumTimelineStep
                key={index}
                instruction={instruction}
                stepNumber={index + 1}
                isActive={activeStep === index}
                isCompleted={completedSteps[index]}
                onPress={() => handleStepPress(index)}
                onTimerPress={() => handleTimerPress(index)}
                index={index}
              />
            ))}
          </View>
        </Animated.View>

        {/* Nutrition Section */}
        {recipe.nutrition && (
          <Animated.View entering={SlideInUp.delay(500).duration(600)} style={styles.modernSection}>
            <TouchableOpacity
              style={styles.modernSectionHeader}
              onPress={() => setShowNutrition(!showNutrition)}
            >
              <Text style={styles.modernSectionTitle}>Nutrition Facts</Text>
              <Ionicons
                name={showNutrition ? "chevron-up" : "chevron-down"}
                size={20}
                color={Colors.mutedForeground}
              />
            </TouchableOpacity>

            {showNutrition && (
              <Animated.View entering={SlideInUp.duration(400)} style={styles.nutritionContainer}>
                <ModernCard title="Nutrition" variant="glass" style={styles.nutritionCard}>
                  <View style={styles.nutritionGrid}>
                    <NutritionRing
                      label="Protein"
                      value={recipe.nutrition.protein}
                      maxValue={100}
                      color={Colors.success}
                      unit="g"
                      index={0}
                    />
                    <NutritionRing
                      label="Carbs"
                      value={recipe.nutrition.carbs}
                      maxValue={150}
                      color={Colors.warning}
                      unit="g"
                      index={1}
                    />
                    <NutritionRing
                      label="Fat"
                      value={recipe.nutrition.fat}
                      maxValue={80}
                      color={Colors.error}
                      unit="g"
                      index={2}
                    />
                    <NutritionRing
                      label="Fiber"
                      value={recipe.nutrition.fiber}
                      maxValue={30}
                      color={Colors.info}
                      unit="g"
                      index={3}
                    />
                  </View>
                </ModernCard>
              </Animated.View>
            )}
          </Animated.View>
        )}

        {/* Action Buttons */}
        <Animated.View entering={SlideInUp.delay(600).duration(600)} style={styles.actionSection}>
          <ModernButton
            title="Start Cooking"
            onPress={startCooking}
            variant="primary"
            size="xl"
            icon="restaurant"
            fullWidth
            style={styles.primaryAction}
          />

          <View style={styles.secondaryActions}>
            <ModernButton
              title="Set Timer"
              onPress={() => setShowTimer(true)}
              variant="outline"
              size="md"
              icon="timer"
              style={styles.secondaryAction}
            />
            <ModernButton
              title="Share Recipe"
              onPress={shareRecipe}
              variant="outline"
              size="md"
              icon="share"
              style={styles.secondaryAction}
            />
          </View>
        </Animated.View>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Timer Modal */}
      <ModernModal
        visible={showTimer}
        onClose={() => setShowTimer(false)}
        title="Set Cooking Timer"
        variant="center"
        size="md"
      >
        <View style={styles.timerModalContent}>
          <Text style={styles.timerModalText}>
            Timer for step {timerStep + 1}
          </Text>
          <ModernButton
            title="Start 15 min timer"
            onPress={() => {
              setShowTimer(false);
              // Navigate to timer screen
            }}
            variant="primary"
            size="md"
            icon="timer"
          />
        </View>
      </ModernModal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff', // Clean white background
  },

  // Beautiful Background
  backgroundContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  whiteOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.87)', // Reduced opacity by 10%
  },

  // Hero Section
  heroSection: {
    height: height * 0.35, // Reduced from 50% to 35% for better space usage
    position: 'relative',
  },
  heroImage: {
    flex: 1,
    width: '100%',
  },
  heroGradient: {
    flex: 1,
    justifyContent: 'space-between',
    paddingTop: 60, // Reduced by 20px
    paddingHorizontal: Spacing.xl,
    paddingBottom: Spacing.xl,
  },
  heroHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  backButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  heroActions: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  actionButtonActive: {
    backgroundColor: 'rgba(255, 107, 107, 0.2)',
  },
  heroContent: {
    gap: Spacing.lg,
  },
  heroTitle: {
    fontSize: 32,
    fontWeight: '700',
    color: 'white',
    marginBottom: 12,
    letterSpacing: -0.5,
    textShadowColor: 'rgba(0, 0, 0, 0.7)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  heroDescription: {
    fontSize: 18,
    color: 'white',
    opacity: 0.95,
    lineHeight: 26,
    fontWeight: '500',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 3,
  },
  heroStats: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
    marginTop: 8,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
  },
  statIconContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  statText: {
    fontSize: 14,
    fontWeight: '600',
    color: 'white',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  difficultyDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },

  // Main Content
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 120,
  },

  // Beautiful Modern Servings Section
  modernServingsSection: {
    paddingHorizontal: 20,
    paddingTop: 80, // Consistent with other screens
    marginBottom: 24,
  },
  modernServingsCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 32,
    padding: 28,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.15,
    shadowRadius: 24,
    elevation: 15,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  modernServingsHeader: {
    marginBottom: 24,
    alignItems: 'center',
  },
  modernServingsTitle: {
    fontSize: 22,
    fontWeight: '700',
    color: '#1a202c',
    marginBottom: 6,
    letterSpacing: -0.4,
  },
  modernServingsSubtitle: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  modernServingsControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 32,
  },
  modernServingsButton: {
    width: 52,
    height: 52,
    borderRadius: 26,
    backgroundColor: '#6B7C5A',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#6B7C5A',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 12,
  },
  modernServingsButtonDisabled: {
    backgroundColor: '#E5E7EB',
    shadowOpacity: 0.1,
  },
  modernServingsDisplay: {
    alignItems: 'center',
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    borderRadius: 24,
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderWidth: 2,
    borderColor: '#6B7C5A',
  },
  modernServingsNumber: {
    fontSize: 28,
    fontWeight: '700',
    color: '#6B7C5A',
    letterSpacing: -0.5,
  },
  modernServingsLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: '#6B7C5A',
    marginTop: 2,
  },
  // Beautiful Modern Sections
  modernSection: {
    paddingHorizontal: 20,
    marginBottom: 32,
  },
  modernSectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  modernSectionTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1a202c',
    letterSpacing: -0.4,
  },
  modernProgressIndicator: {
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  modernProgressText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#6B7C5A',
  },

  // Beautiful Ingredients Card
  modernIngredientsCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 32,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.15,
    shadowRadius: 24,
    elevation: 15,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },

  // Modern Ingredient Items
  modernIngredientItem: {
    marginBottom: 16,
  },
  modernIngredientButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'rgba(107, 124, 90, 0.05)',
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.1)',
  },
  ingredientIcon: {
    width: 32,
    height: 32,
    borderRadius: 8,
    backgroundColor: Colors.brandMuted,
    marginRight: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modernIngredientText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    color: Colors.foreground,
    lineHeight: 22,
  },

  // Clean Premium Timeline Section
  premiumTimelineSection: {
    marginHorizontal: 20,
    marginBottom: 32,
  },
  premiumTimelineHeader: {
    marginBottom: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 20,
    padding: 20,
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.1)',
  },
  premiumTimelineHeaderText: {
    alignItems: 'center',
  },
  premiumTimelineTitle: {
    fontSize: 24,
    fontWeight: '800',
    color: '#6B7C5A',
    letterSpacing: -0.5,
    marginBottom: 8,
    textAlign: 'center',
  },
  premiumTimelineSubtitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#8B9A7A',
    lineHeight: 22,
    textAlign: 'center',
    marginBottom: 16,
  },
  premiumProgressContainer: {
    alignItems: 'center',
  },
  premiumProgressRing: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    borderWidth: 4,
    borderColor: '#6B7C5A',
    justifyContent: 'center',
    alignItems: 'center',
  },
  premiumProgressText: {
    fontSize: 18,
    fontWeight: '800',
    color: '#6B7C5A',
  },
  premiumTimelineContainer: {
    gap: 16,
  },

  // Clean Premium Timeline Step Styles
  premiumTimelineStep: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 20,
    padding: 20,
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.1)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 8,
  },
  premiumTimelineStepActive: {
    borderColor: '#6B7C5A',
    borderWidth: 2,
    backgroundColor: 'rgba(107, 124, 90, 0.03)',
    shadowOpacity: 0.15,
  },
  premiumTimelineStepCompleted: {
    backgroundColor: 'rgba(34, 197, 94, 0.05)',
    borderColor: '#22C55E',
    borderWidth: 2,
  },

  // Premium Step Components
  premiumStepHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  premiumStepMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  premiumStepBadge: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    borderWidth: 2,
    borderColor: 'rgba(107, 124, 90, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  premiumStepBadgeActive: {
    backgroundColor: '#6B7C5A',
    borderColor: '#6B7C5A',
  },
  premiumStepBadgeCompleted: {
    backgroundColor: '#22C55E',
    borderColor: '#22C55E',
  },
  premiumStepBadgeText: {
    fontSize: 14,
    fontWeight: '700',
    color: '#8B9A7A',
  },
  premiumStepBadgeTextActive: {
    color: 'white',
  },
  premiumStepNumber: {
    fontSize: 16,
    fontWeight: '700',
    color: '#8B9A7A',
    letterSpacing: 0.5,
    textTransform: 'uppercase',
  },
  premiumStepNumberActive: {
    color: '#6B7C5A',
  },
  premiumStepNumberCompleted: {
    color: '#22C55E',
  },
  premiumTimerChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  premiumTimerText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#6B7C5A',
  },
  premiumCompletedBadge: {
    opacity: 0.8,
  },
  premiumStepInstruction: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1F2937',
    lineHeight: 24,
    marginBottom: 12,
  },
  premiumStepInstructionActive: {
    fontWeight: '600',
    color: '#111827',
  },
  premiumStepInstructionCompleted: {
    color: '#6B7280',
  },
  premiumSubstepsContainer: {
    marginTop: 12,
    paddingLeft: 8,
  },
  premiumSubstepItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  premiumSubstepIndicator: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#6B7C5A',
    marginTop: 8,
    marginRight: 12,
  },
  premiumSubstepText: {
    fontSize: 14,
    fontWeight: '400',
    color: '#4B5563',
    lineHeight: 20,
    flex: 1,
  },
  premiumSubstepTextActive: {
    color: '#374151',
    fontWeight: '500',
  },
  premiumSubstepTextCompleted: {
    color: '#9CA3AF',
  },
  premiumProgressBar: {
    height: 4,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    borderRadius: 2,
    marginTop: 12,
    overflow: 'hidden',
  },
  premiumProgressBarFill: {
    height: '100%',
    backgroundColor: '#6B7C5A',
    borderRadius: 2,
  },



  // Legacy Instructions Container (keeping for compatibility)
  modernInstructionsContainer: {
    gap: 16,
  },

  // Legacy Modern Instruction Steps (keeping for compatibility)
  modernInstructionStep: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.1,
    shadowRadius: 16,
    elevation: 12,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    marginBottom: 16,
  },
  modernInstructionStepActive: {
    borderColor: '#6B7C5A',
    borderWidth: 2,
    shadowColor: '#6B7C5A',
    shadowOpacity: 0.2,
  },
  modernInstructionStepCompleted: {
    backgroundColor: 'rgba(107, 124, 90, 0.05)',
    borderColor: '#6B7C5A',
  },
  modernInstructionButton: {
    padding: 20,
  },
  modernStepHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 16,
  },
  modernStepNumber: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(107, 124, 90, 0.08)',
    borderWidth: 2,
    borderColor: 'rgba(107, 124, 90, 0.15)',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#6B7C5A',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  modernStepNumberActive: {
    backgroundColor: '#6B7C5A',
    borderColor: '#6B7C5A',
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 6,
    transform: [{ scale: 1.05 }],
  },
  modernStepNumberCompleted: {
    backgroundColor: '#22c55e',
    borderColor: '#22c55e',
    shadowColor: '#22c55e',
    shadowOpacity: 0.2,
  },
  modernStepNumberText: {
    fontSize: 18,
    fontWeight: '800',
    color: '#6B7C5A',
  },
  modernStepNumberTextActive: {
    color: 'white',
    fontSize: 19,
  },
  modernStepContent: {
    flex: 1,
  },
  // Enhanced Instruction Typography
  modernInstructionHeading: {
    fontSize: 18,
    fontWeight: '700',
    color: '#6B7C5A',
    marginBottom: 8,
    letterSpacing: 0.5,
  },
  modernInstructionHeadingActive: {
    color: '#5A6B4A',
    fontSize: 19,
  },
  modernInstructionHeadingCompleted: {
    color: '#8B9A7A',
  },
  modernInstructionText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1a202c',
    lineHeight: 26,
    marginBottom: 4,
  },
  modernInstructionTextActive: {
    color: '#0f172a',
    fontWeight: '600',
    fontSize: 17,
  },
  modernInstructionTextCompleted: {
    color: '#6B7280',
    textDecorationLine: 'line-through',
    textDecorationColor: '#9CA3AF',
  },

  // Sub-steps Styling
  modernSubstepsContainer: {
    marginTop: 12,
    paddingLeft: 8,
  },
  modernSubstepItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  modernSubstepBullet: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#6B7C5A',
    marginTop: 8,
    marginRight: 12,
  },
  modernSubstepText: {
    fontSize: 14,
    fontWeight: '400',
    color: '#4B5563',
    lineHeight: 22,
    flex: 1,
  },
  modernSubstepTextActive: {
    color: '#374151',
    fontWeight: '500',
  },
  modernSubstepTextCompleted: {
    color: '#9CA3AF',
  },
  modernTimerButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  progressIndicator: {
    backgroundColor: Colors.brandMuted,
    borderRadius: BorderRadius.full,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
  },
  progressText: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    color: Colors.brand,
  },

  // Ingredients
  ingredientsCard: {
    padding: Spacing.lg,
  },
  ingredientItem: {
    marginBottom: Spacing.md,
  },
  ingredientButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.md,
  },
  ingredientText: {
    fontSize: FontSizes.base,
    color: Colors.foreground,
    flex: 1,
    lineHeight: 20,
  },

  // Instructions
  instructionsContainer: {
    gap: Spacing.lg,
  },
  instructionStep: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.xl,
    padding: Spacing.lg,
    borderWidth: 1,
    borderColor: Colors.border,
    ...Shadows.sm,
  },
  instructionStepActive: {
    borderColor: Colors.brand,
    backgroundColor: Colors.brandMuted,
  },
  instructionStepCompleted: {
    backgroundColor: '#22c55e',
    borderColor: Colors.success,
  },
  instructionButton: {
    flex: 1,
  },
  stepHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: Spacing.lg,
  },
  stepNumber: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.muted,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: Spacing.xs,
  },
  stepNumberActive: {
    backgroundColor: Colors.brand,
  },
  stepNumberCompleted: {
    backgroundColor: Colors.success,
  },
  stepNumberText: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.bold,
    color: Colors.mutedForeground,
  },
  stepNumberTextActive: {
    color: Colors.brandForeground,
  },
  stepContent: {
    flex: 1,
  },
  instructionText: {
    fontSize: FontSizes.base,
    color: Colors.foreground,
    lineHeight: 24,
  },
  instructionTextActive: {
    color: Colors.foreground,
    fontWeight: FontWeights.medium,
  },
  instructionTextCompleted: {
    color: Colors.mutedForeground,
  },
  timerButton: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.lg,
    backgroundColor: Colors.brandMuted,
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Nutrition
  nutritionContainer: {
    marginTop: Spacing.lg,
  },
  nutritionCard: {
    padding: Spacing.xl,
  },
  nutritionGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    flexWrap: 'wrap',
    gap: Spacing.lg,
  },
  nutritionRing: {
    alignItems: 'center',
    width: 80,
  },
  ringContainer: {
    width: 60,
    height: 60,
    position: 'relative',
    marginBottom: Spacing.md,
  },
  ringBackground: {
    position: 'absolute',
    width: 60,
    height: 60,
    borderRadius: 30,
    borderWidth: 4,
  },
  ringProgress: {
    position: 'absolute',
    width: 60,
    height: 60,
    borderRadius: 30,
    borderWidth: 4,
    borderTopColor: 'transparent',
    borderRightColor: 'transparent',
    borderBottomColor: 'transparent',
  },
  ringCenter: {
    position: 'absolute',
    width: 60,
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
  ringValue: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.bold,
  },
  ringUnit: {
    fontSize: FontSizes.xs,
    color: Colors.mutedForeground,
  },
  ringLabel: {
    fontSize: FontSizes.sm,
    color: Colors.foreground,
    textAlign: 'center',
    fontWeight: FontWeights.medium,
  },

  // Actions
  actionSection: {
    paddingHorizontal: Spacing.xl,
    gap: Spacing.lg,
  },
  primaryAction: {
    marginBottom: Spacing.md,
  },
  secondaryActions: {
    flexDirection: 'row',
    gap: Spacing.md,
  },
  secondaryAction: {
    flex: 1,
  },

  // Timer Modal
  timerModalContent: {
    padding: Spacing.xl,
    alignItems: 'center',
    gap: Spacing.lg,
  },
  timerModalText: {
    fontSize: FontSizes.lg,
    color: Colors.foreground,
    textAlign: 'center',
  },

  // Bottom Spacing
  bottomSpacing: {
    height: 40,
  },
  // Apple-Inspired Natural Header (Not Fixed!)
  appleNaturalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 80, // Consistent with other screens
    paddingHorizontal: 20,
    paddingBottom: 20,
    backgroundColor: 'white',
  },
  appleBackButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  appleHeaderActions: {
    flexDirection: 'row',
    gap: 8,
  },
  appleActionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  appleActionButtonActive: {
    backgroundColor: 'rgba(107, 124, 90, 0.15)', // Use app's green theme instead of red
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.3)',
  },
  appleActionButtonPressed: {
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    transform: [{ scale: 0.95 }],
  },

  // Apple-Inspired Hero Section
  appleHeroSection: {
    paddingHorizontal: 20,
    paddingTop: 80, // Consistent with other screens
    paddingBottom: 32,
    backgroundColor: '#F8F9FA',
  },
  appleImageContainer: {
    borderRadius: 32,
    overflow: 'hidden',
    shadowColor: '#6B7C5A',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.25,
    shadowRadius: 24,
    elevation: 16,
    marginBottom: 24,
  },
  appleRecipeImage: {
    width: '100%',
    height: 320,
    justifyContent: 'flex-end',
  },
  appleImageStyle: {
    borderRadius: 32,
  },
  appleImageGradient: {
    flex: 1,
    justifyContent: 'flex-end',
    padding: 28,
  },
  appleImageContent: {
    gap: 8,
  },
  appleRecipeTitle: {
    fontSize: 32,
    fontWeight: '800',
    color: 'white',
    lineHeight: 36,
    letterSpacing: -0.5,
    textShadowColor: 'rgba(0, 0, 0, 0.8)',
    textShadowOffset: { width: 0, height: 3 },
    textShadowRadius: 12,
  },
  appleRecipeSubtitle: {
    fontSize: 17,
    color: 'rgba(255, 255, 255, 0.95)',
    lineHeight: 24,
    fontWeight: '400',
    letterSpacing: -0.2,
  },

  // Elegant Stats Cards
  appleStatsContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  appleStatCard: {
    flex: 1,
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.06,
    shadowRadius: 12,
    elevation: 6,
  },
  appleStatIcon: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  appleStatLabel: {
    fontSize: 13,
    color: '#86868B',
    fontWeight: '500',
    marginBottom: 4,
    textAlign: 'center',
  },
  appleStatValue: {
    fontSize: 16,
    color: '#1D1D1F',
    fontWeight: '700',
    textAlign: 'center',
  },
});

export default RecipeDetailScreenModern;
